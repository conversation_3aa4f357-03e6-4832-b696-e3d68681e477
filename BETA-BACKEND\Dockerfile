GNU nano 7.2                                                                      Dockerfile                                                                               
# Use an official Python 3.9 slim image as the base
FROM python:3.9-slim

# Install netcat-openbsd, which provides the 'nc' command for the wait script
RUN apt-get update && apt-get install -y netcat-openbsd --no-install-recommends && rm -rf /var/lib/apt/lists/*

# Set the working directory inside the container to /app
WORKDIR /app

# Add the working directory to the PYTHONPATH to ensure modules are found
ENV PYTHONPATH="/app"

# Copy the requirements file to install dependencies first
COPY requirements.txt .

# Install the Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the wait script into the container
COPY wait-for.sh /usr/local/bin/wait-for.sh

# Copy the rest of your application code into the image
COPY . .

# Expose port 8000 to allow communication with the app
EXPOSE 8000


