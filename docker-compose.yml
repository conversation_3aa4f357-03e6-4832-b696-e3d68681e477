version: '3.8'

services:
  # The FastAPI backend service
  backend:
    build: ./backend
    volumes:
      - ./backend:/app
    env_file:
      - ./backend/.env.production
    command: ["/usr/local/bin/wait-for.sh", "db", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app-network
  # The React frontend service
  frontend:
    build: ./frontend
    volumes:
      - ./frontend:/app
    networks:
      - app-network

  # The MySQL database service
  db:
    image: mysql:8.0
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: lawvriksh_referral
      MYSQL_USER: lawuser
      MYSQL_PASSWORD: lawpass123
    volumes:
      - db-data:/var/lib/mysql
      - ./backend/lawdata.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  # The Nginx reverse proxy service
  nginx:
    image: nginx:latest
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    depends_on:
      - frontend
      - backend
    networks:
      - app-network

  # The Certbot service for SSL certificates
  certbot:
    image: certbot/certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"

# Defines the named volumes
volumes:
  db-data:

# Defines the shared network for inter-container communication
networks:
  app-network:
    driver: bridge


