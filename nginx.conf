GNU nano 7.2                                                                                                                                                                                                                                                                                                                                                                                                                                                                              nginx.conf                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
# Redirects lawvriksh.com and www.lawvriksh.com from HTTP to HTTPS
server {
    listen 80;
    server_name lawvriksh.com www.lawvriksh.com;

    # Route for Let's Encrypt certificate validation
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# Main server block for HTTPS traffic
server {
    listen 443 ssl;
    server_name lawvriksh.com www.lawvriksh.com;

    # --- THIS IS THE KEY CHANGE ---
    # Point to the certificate directory named after the primary domain
    ssl_certificate /etc/letsencrypt/live/lawvriksh.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/lawvriksh.com/privkey.pem;

    # Proxy to the frontend container
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Proxy to the backend container
    location /api {
        proxy_pass http://backend:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}





